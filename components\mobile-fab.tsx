"use client";

import { useState } from "react";
import { Plus, ShoppingCart, QrCode, X } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { useRouter } from "next/navigation";

export const MobileFAB = () => {
  const [isOpen, setIsOpen] = useState(false);
  const router = useRouter();

  const handleSelectSell = () => {
    router.push("/select-sale");
    setIsOpen(false);
  };

  const handleScanSell = () => {
    router.push("/scan-sale");
    setIsOpen(false);
  };

  return (
    <div className="fixed bottom-6 right-6 z-50 md:hidden">
      {/* Overlay */}
      {isOpen && (
        <div 
          className="fixed inset-0 bg-black/20 -z-10"
          onClick={() => setIsOpen(false)}
        />
      )}

      {/* Action Buttons */}
      {isOpen && (
        <div className="absolute bottom-16 right-0 space-y-3">
          {/* Select & Sell Button */}
            <div className="flex items-center gap-3">
            <div className="bg-bg-blue-2  00 px-3 py-2 rounded-lg shadow-lg">
              <span className="text-sm font-medium text-gray-700">Select & Sell</span>
            </div>
            <Button
              onClick={handleSelectSell}
              size="lg"
              className="w-14 h-14 rounded-full bg-blue-500 hover:bg-blue-600 shadow-lg"
            >
              <ShoppingCart className="w-6 h-6 text-white" />
            </Button>
          </div>

          {/* Scan & Sell Button */}
          <div className="flex items-center gap-3">
            <div className="bg-secondary px-3 py-2 rounded-lg shadow-lg">
              <span className="text-sm font-medium text-gray-700">Scan & Sell</span>
            </div>
            <Button
              onClick={handleScanSell}
              size="lg"
              className="w-14 h-14 rounded-full bg-green-500 hover:bg-green-600 shadow-lg"
            >
              <QrCode className="w-6 h-6 text-white" />
            </Button>
          </div>
        </div>
      )}

      {/* Main FAB Button */}
      <Button
        onClick={() => setIsOpen(!isOpen)}
        size="lg"
        className={`w-16 h-16 rounded-full shadow-lg transition-all duration-300 ${
          isOpen 
            ? "bg-primary hover:bg-green-300 rotate-45" 
            : "bg-primary hover:bg-primary/90"
        }`}
      >
        {isOpen ? (
          <X className="w-7 h-7 text-white" />
        ) : (
          <Plus className="w-7 h-7 text-white" />
        )}
      </Button>
    </div>
  );
};
