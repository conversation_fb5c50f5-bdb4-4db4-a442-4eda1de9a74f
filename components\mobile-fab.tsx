"use client";

import { useState } from "react";
import { Plus, ShoppingCart, QrCode, X, Coins } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";

export const MobileFAB = () => {
  const [isOpen, setIsOpen] = useState(false);
  const router = useRouter();

  const handleSelectSell = () => {
    router.push("/select-sale");
    setIsOpen(false);
  };

  const handleScanSell = () => {
    router.push("/scan-sale");
    setIsOpen(false);
  };

  const handleBuyTokens = () => {
    router.push("/buy-tokens");
    setIsOpen(false);
  };

  return (
    <div className="fixed bottom-6 right-6 z-50 md:hidden">
      {/* Overlay */}
      {isOpen && (
        <div 
          className="fixed inset-0 bg-black/20 -z-10"
          onClick={() => setIsOpen(false)}
        />
      )}

      {/* Action Buttons */}
      <div className="absolute bottom-18 right-0 space-y-3">
        

        {/* Select & Sell Button - Left aligned (zig-zag) */}
        <div
          className={`flex items-center gap-3 justify-start transition-all duration-300 ${
            isOpen
              ? "opacity-100 translate-y-0 translate-x-0"
              : "opacity-0 translate-y-4 -translate-x-4"
          }`}
          style={{ transitionDelay: isOpen ? "100ms" : "100ms" }}
        >
          <div className="bg-blue-300 px-4 py-2 min-w-[110px] rounded-lg shadow-lg flex items-center justify-center">
            <span className="text-sm font-medium text-gray-700 whitespace-nowrap">Select & Sell</span>
          </div>
          <Button
            onClick={handleSelectSell}
            size="lg"
            className="w-14 h-14 rounded-full bg-blue-500 hover:bg-blue-600 shadow-lg"
          >
            <ShoppingCart className="w-6 h-6 text-white" />
          </Button>
        </div>

        {/* Scan & Sell Button - Right aligned */}
        <div
          className={`flex items-center gap-3 justify-end transition-all duration-300 ${
            isOpen
              ? "opacity-100 translate-y-0 translate-x-0"
              : "opacity-0 translate-y-4 translate-x-4"
          }`}
          style={{ transitionDelay: isOpen ? "200ms" : "0ms" }}
        >
          <div className="bg-green-300 px-4 py-2 min-w-[110px] rounded-lg shadow-lg flex items-center justify-center">
            <span className="text-sm font-medium text-gray-700 whitespace-nowrap">Scan & Sell</span>
          </div>
          <Button
            onClick={handleScanSell}
            size="lg"
            className="w-14 h-14 rounded-full bg-green-500 hover:bg-green-600 shadow-lg"
          >
            <QrCode className="w-6 h-6 text-white" />
          </Button>
        </div>
      </div>

      {/* Main FAB Button */}
      <Button
        onClick={() => setIsOpen(!isOpen)}
        size="lg"
        className={`w-16 h-16 rounded-full shadow-lg transition-all duration-300 ${
          isOpen 
            ? "bg-primary hover:bg-green-300 rotate-45" 
            : "bg-primary hover:bg-primary/90"
        }`}
      >
        {isOpen ? (
          <X className="w-7 h-7 text-white" />
        ) : (
          <Plus className="w-7 h-7 text-white" />
        )}
      </Button>
    </div>
  );
};
