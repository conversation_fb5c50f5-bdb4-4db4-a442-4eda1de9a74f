"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Coins, Plus } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

interface TokenDisplayProps {
  tokens: number;
  onAddTokens?: () => void;
}

export const TokenDisplay = ({ tokens, onAddTokens }: TokenDisplayProps) => {
  const formatTokens = (amount: number) => {
    return amount.toLocaleString();
  };

  return (
    <Card className="border-none shadow-lg bg-gradient-to-br from-yellow-50 to-amber-50 hover:shadow-xl transition-all duration-300">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium text-muted-foreground">
          Available Tokens
        </CardTitle>
        <Coins className="h-5 w-5 text-amber-600" />
      </CardHeader>
      <CardContent>
        <div className="flex items-center justify-between">
          <div>
            <div className="text-3xl font-bold text-amber-700 mb-1">
              {formatTokens(tokens)}
            </div>
            <Badge variant="secondary" className="bg-amber-100 text-amber-800">
              {tokens >= 12 ? "Active" : "Low Balance"}
            </Badge>
          </div>
          {onAddTokens && (
            <Button
              size="sm"
              variant="outline"
              onClick={onAddTokens}
              className="border-amber-300 text-amber-700 hover:bg-amber-100"
            >
              <Plus className="h-4 w-4 mr-1" />
              Add
            </Button>
          )}
        </div>
        <div className="mt-3 text-xs text-muted-foreground">
          {tokens >= 12 
            ? "You have sufficient tokens for operations" 
            : "Consider adding more tokens for uninterrupted service"
          }
        </div>
      </CardContent>
    </Card>
  );
};
