import { UserButton } from "@/features/auth/components/user-button";
import { MobileSidebar } from "./mobile-sidebar";
import { OrganizationSelector } from "./organization-selector";
import { useStateManager } from "@/hooks/use-context";
import { usePathname } from "next/navigation";

export const Navbar = () => {
    const { currentOrganization } = useStateManager();
    const pathname = usePathname();

    // Check if we're on an organization-specific page
    const isOrganizationPage = pathname.includes('/organizations/');
    
    // Mock organizations data - replace with real data
    const mockOrganizations: Array<{ id: string; name: string }> = [];

    return (
        <nav className="pt-4 px-6 flex items-center justify-between">
            <div className="flex items-center gap-6">
                <div className="flex-col hidden lg:flex">
                    <h1 className="text-2xl font-semibold">Dashboard</h1>
                    <p className="text-muted-foreground">
                        Monitor your business operations
                    </p>
                </div>

                
            </div>

            <div className="flex items-center gap-4">
                <MobileSidebar />
                {/* Organization Selector */}
                <div className="hidden md:block">
                    <OrganizationSelector
                        currentOrganization={currentOrganization}
                        organizations={mockOrganizations}
                    />
                </div>
                <UserButton />
            </div>
        </nav>
    );
}