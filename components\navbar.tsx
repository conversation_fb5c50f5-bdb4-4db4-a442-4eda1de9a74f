import { UserButton } from "@/features/auth/components/user-button";
import { MobileSidebar } from "./mobile-sidebar";
import { useStateManager } from "@/hooks/use-context";
import { usePathname } from "next/navigation";

export const Navbar = () => {
    const { currentOrganization } = useStateManager();
    const pathname = usePathname();

    // Check if we're on an organization-specific page
    const isOrganizationPage = pathname.includes('/organizations/');

    return (
        <nav className="pt-4 px-6 flex items-center justify-between">
            <div className="flex-col hidden lg:flex">
                <h1 className="text-2xl font-semibold">
                    {isOrganizationPage && currentOrganization
                        ? currentOrganization.name
                        : "Dashboard"
                    }
                </h1>
                <p className="text-muted-foreground">
                    {isOrganizationPage && currentOrganization
                        ? "Monitor your business operations"
                        : "Monitor all of your products here"
                    }
                </p>
            </div>
            <MobileSidebar />
            <UserButton />
        </nav>
    );
}