"use client"

import { useState } from "react";
import { Building2, Users, ArrowRight, Plus } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useStateManager } from "@/hooks/use-context";
import { useGetOrganizations } from "../api/use-get-organizations";
import { useSelectOrganization } from "../api/use-select-organization";

const mockOrganizations = [
  {
    id: 1,
    name: "Tech Solutions Inc",
    description: "Software development and consulting",
    memberCount: 15,
    logo: null
  },
  {
    id: 2,
    name: "Marketing Agency Pro",
    description: "Digital marketing and brand management",
    memberCount: 8,
    logo: null
  }
];

export const ChooseOrganizationCard = () => {
  const [selectedOrg, setSelectedOrg] = useState<number | null>(null);
  const { user } = useStateManager();
  const { data: organizations, isLoading: isLoadingOrgs } = useGetOrganizations();
  const selectOrganization = useSelectOrganization();

  const handleSelectOrganization = (orgId: number) => {
    selectOrganization.mutate(orgId);
  };

  const organizationsList = organizations || user?.organizations || mockOrganizations;

  if (isLoadingOrgs) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading organizations...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <Card className="border-none shadow-2xl bg-white/95 backdrop-blur-sm">
        <CardHeader className="text-center pb-6">
          <div className="w-20 h-20 bg-primary rounded-2xl mx-auto mb-6 flex items-center justify-center">
            <Building2 className="w-10 h-10 text-white" />
          </div>
          <CardTitle className="text-2xl md:text-3xl font-bold text-gray-900 mb-2">
            Your Organizations
          </CardTitle>
          <p className="text-gray-600">
            Select an organization to access your dashboard
          </p>
        </CardHeader>

        <CardContent className="p-6 md:p-8">
          <div className="grid gap-4 md:gap-6">
            {organizationsList.map((org: any) => (
              <div
                key={org.id}
                onClick={() => handleSelectOrganization(org.id)}
                className={`
                  relative p-6 rounded-xl border-2 cursor-pointer transition-all duration-200
                  ${selectedOrg === org.id 
                    ? 'border-primary bg-green-50 shadow-lg' 
                    : 'border-gray-200 hover:border-green-300 hover:bg-green-25'
                  }
                `}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 bg-gradient-to-br from-gray-100 to-gray-200 rounded-lg flex items-center justify-center">
                      {org.logo ? (
                        <img 
                          src={org.logo} 
                          alt={org.name}
                          className="w-full h-full object-cover rounded-lg"
                        />
                      ) : (
                        <Building2 className="w-6 h-6 text-gray-600" />
                      )}
                    </div>
                    
                    <div className="flex-1">
                      <h3 className="font-semibold text-gray-900 text-lg">
                        {org.name}
                      </h3>
                      <p className="text-gray-600 text-sm mt-1">
                        {org.description}
                      </p>
                      <div className="flex items-center mt-2 text-xs text-gray-500">
                        <Users className="w-3 h-3 mr-1" />
                        {org.memberCount} members
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-3">
                    <ArrowRight className="w-5 h-5 text-gray-400" />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      
    </div>
  );
};
