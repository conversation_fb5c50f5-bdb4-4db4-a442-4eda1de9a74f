"use client";

import { useEffect } from "react";
import {
  Package,
  Users,
  ShoppingCart,
  AlertTriangle,
  DollarSign
} from "lucide-react";

import { useStateManager } from "@/hooks/use-context";

import { OrganizationHeader } from "@/features/dashboard/components/organization-header";
import { TokenDisplay } from "@/features/dashboard/components/token-display";
import { SalesDisplay } from "@/features/dashboard/components/sales-display";
import { StatsCard } from "@/features/dashboard/components/stats-card";
import { QuickActions } from "@/features/dashboard/components/quick-actions";

// Mock organization data
const mockOrganization = {
  id: 1,
  name: "sample",
  phone: "0626370989",
  address: "Dar es salaam",
  description: "this is sample",
  featured_image: "/storage/",
  deleted_at: null,
  created_at: "2025-07-29T20:39:46.000000Z",
  updated_at: "2025-07-29T20:39:46.000000Z",
  owner_id: 1,
  team_id: 1,
  country: null,
  region: null,
  district: null,
  ward: null,
  street: null,
  latitude: null,
  longitude: null,
  tokens: 0,
  users: [
    {
      id: 1,
      name: "<PERSON>",
      email: "mark<PERSON><PERSON><PERSON>@gmail.com",
      email_verified_at: "2025-07-28T07:55:24.000000Z",
      created_at: "2025-07-28T07:55:24.000000Z",
      updated_at: "2025-07-28T07:55:24.000000Z",
      deleted_at: null,
      active_team_id: null,
      phone: "+255654303353",
      phone_verified_at: null,
      needs_password_change: true,
      apple_id: null,
      google_id: null,
      facebook_id: null,
      pivot: {
        organization_id: 1,
        user_id: 1
      }
    }
  ],
  owner: {
    id: 1,
    name: "Mark Mayalla",
    email: "<EMAIL>",
    email_verified_at: "2025-07-28T07:55:24.000000Z",
    created_at: "2025-07-28T07:55:24.000000Z",
    updated_at: "2025-07-28T07:55:24.000000Z",
    deleted_at: null,
    active_team_id: null,
    phone: "+255654303353",
    phone_verified_at: null,
    needs_password_change: true,
    apple_id: null,
    google_id: null,
    facebook_id: null
  },
  domains: []
};

// Mock dashboard stats
const mockStats = {
  todaySales: 12.89,
  totalSales: 45678.90,
  totalCustomers: 156,
  totalProducts: 89,
  totalOrders: 234,
  lowStockItems: 5,
  recentSales: [],
  topProducts: [],
  salesTrend: []
};

const OrganizationDashboard = () => {
  const organizationIdString = localStorage.getItem("current_organization");
  const organizationId = organizationIdString ? Number(organizationIdString) : undefined;
  const { setCurrentOrganization } = useStateManager();

  useEffect(() => {
    // Set the mock organization in context
    setCurrentOrganization(mockOrganization);
  }, [setCurrentOrganization]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-50">
      <div className="container mx-auto px-4 py-8 space-y-8">
        

        {/* Organization Header and Tokens Row */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Organization Header - Takes 3 columns on large screens */}
          <div className="lg:col-span-3">
            <OrganizationHeader organization={mockOrganization} />
          </div>

          {/* Token Display - Takes 1 column on large screens */}
          <div className="lg:col-span-1">
            <TokenDisplay
              tokens={mockOrganization.tokens}
              onAddTokens={() => {
                console.log("Add tokens clicked");
              }}
            />
          </div>
        </div>

        {/* Key Metrics Section */}
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-semibold text-gray-900">Key Metrics</h2>
            <div className="text-sm text-muted-foreground">
              Last updated: {new Date().toLocaleTimeString()}
            </div>
          </div>

          {/* Primary Metrics Grid */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* Today's Sales - Takes 2 columns */}
            <div className="lg:col-span-2">
              <SalesDisplay
                todaySales={mockStats.todaySales}
                totalSales={mockStats.totalSales}
                trend={{
                  value: 15.2,
                  isPositive: true
                }}
              />
            </div>

            {/* Total Customers */}
            <StatsCard
              title="Total Customers"
              value={mockStats.totalCustomers}
              icon={Users}
              description="Active customers"
              trend={{
                value: 8.5,
                isPositive: true
              }}
              className="bg-gradient-to-br from-blue-50 via-blue-25 to-cyan-50 border border-blue-100 hover:border-blue-200 hover:shadow-lg transition-all duration-300"
              valueClassName="text-blue-700"
            />

            {/* Total Products */}
            <StatsCard
              title="Products in Stock"
              value={mockStats.totalProducts}
              icon={Package}
              description="Available items"
              trend={{
                value: 3.2,
                isPositive: true
              }}
              className="bg-gradient-to-br from-purple-50 via-purple-25 to-violet-50 border border-purple-100 hover:border-purple-200 hover:shadow-lg transition-all duration-300"
              valueClassName="text-purple-700"
            />
          </div>

          {/* Secondary Metrics Grid */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* Total Orders */}
            <StatsCard
              title="Total Orders"
              value={mockStats.totalOrders}
              icon={ShoppingCart}
              description="All time orders"
              className="bg-gradient-to-br from-orange-50 via-orange-25 to-amber-50 border border-orange-100 hover:border-orange-200 hover:shadow-lg transition-all duration-300"
              valueClassName="text-orange-700"
            />

            {/* Monthly Revenue - Takes 2 columns */}
            <div className="sm:col-span-2">
              <StatsCard
                title="Monthly Revenue"
                value={`TSH ${mockStats.totalSales.toLocaleString()}/=`}
                icon={DollarSign}
                description="This month's earnings"
                trend={{
                  value: 12.3,
                  isPositive: true
                }}
                className="bg-gradient-to-br from-green-50 via-green-25 to-emerald-50 border border-green-100 hover:border-green-200 hover:shadow-lg transition-all duration-300 h-full"
                valueClassName="text-green-700 text-2xl lg:text-3xl"
              />
            </div>

            
          </div>
        </div>

        {/* Quick Actions Section */}
        <div className="space-y-6">
          <h2 className="text-2xl font-semibold text-gray-900">Quick Actions</h2>
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6 hover:shadow-md transition-shadow duration-300">
            {organizationId !== undefined && (
              <QuickActions organizationId={organizationId} />
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default OrganizationDashboard;
