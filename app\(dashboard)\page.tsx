"use client";

import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useStateManager } from '@/hooks/use-context';

const DashboardPage = () => {
  const router = useRouter();
  const { user, isLoading, currentOrganization } = useStateManager();

  useEffect(() => {
    if (!isLoading && user) {
      // If user has organizations but no current organization is selected
      if (user.organizations && user.organizations.length > 0 && !currentOrganization) {
        // If user has only one organization, redirect directly to it
        if (user.organizations.length === 1) {
          router.push(`/organizations/${user.organizations[0].id}`);
        } else {
          // If user has multiple organizations, let them choose
          router.push('/setup/choose-organization');
        }
      } else if (!user.organizations || user.organizations.length === 0) {
        // If user has no organizations, redirect to create one
        router.push('/setup/create-organization');
      } else if (currentOrganization) {
        // If current organization is set, redirect to its dashboard
        router.push(`/organizations/${currentOrganization.id}`);
      }
    }
  }, [user, isLoading, currentOrganization, router]);

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">Welcome to Izi Sales</h1>
        <p className="text-muted-foreground">Redirecting to your dashboard...</p>
      </div>
    </div>
  );
};

export default DashboardPage;