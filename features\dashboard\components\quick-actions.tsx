"use client";

import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { 
  ShoppingCart, 
  Package, 
  Users, 
  CreditCard, 
  TrendingDown, 
  ShoppingBag,
  BarChart3,
  Plus
} from "lucide-react";
import { useRouter } from "next/navigation";

interface QuickActionsProps {
  organizationId: number;
}

export const QuickActions = ({ organizationId }: QuickActionsProps) => {
  const router = useRouter();

  const actions = [
    {
      label: "New Sale",
      icon: ShoppingCart,
      href: `/organizations/${organizationId}/sales/new`,
      color: "bg-green-500 hover:bg-green-600",
      description: "Create a new sale"
    },
    {
      label: "Add Product",
      icon: Package,
      href: `/organizations/${organizationId}/inventory/new`,
      color: "bg-blue-500 hover:bg-blue-600",
      description: "Add new inventory"
    },
    {
      label: "New Customer",
      icon: Users,
      href: `/organizations/${organizationId}/customers/new`,
      color: "bg-purple-500 hover:bg-purple-600",
      description: "Register customer"
    },
    {
      label: "Record Purchase",
      icon: ShoppingBag,
      href: `/organizations/${organizationId}/purchases/new`,
      color: "bg-orange-500 hover:bg-orange-600",
      description: "Add new purchase"
    },
    {
      label: "Add Expense",
      icon: TrendingDown,
      href: `/organizations/${organizationId}/expenses/new`,
      color: "bg-red-500 hover:bg-red-600",
      description: "Record expense"
    },
    {
      label: "View Reports",
      icon: BarChart3,
      href: `/organizations/${organizationId}/reports`,
      color: "bg-indigo-500 hover:bg-indigo-600",
      description: "Business analytics"
    }
  ];

  return (
    <Card className="border-none shadow-lg">
      <CardHeader>
        <CardTitle className="text-lg font-semibold flex items-center">
          <Plus className="h-5 w-5 mr-2" />
          Quick Actions
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
          {actions.map((action) => (
            <Button
              key={action.label}
              variant="outline"
              className="h-auto p-4 flex flex-col items-center space-y-2 hover:shadow-md transition-all duration-200"
              onClick={() => router.push(action.href)}
            >
              <div className={`p-2 rounded-lg ${action.color} text-white`}>
                <action.icon className="h-5 w-5" />
              </div>
              <div className="text-center">
                <div className="font-medium text-sm">{action.label}</div>
                <div className="text-xs text-muted-foreground">{action.description}</div>
              </div>
            </Button>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};
