"use client";

import { Building2, <PERSON>P<PERSON>, Phone, Users } from "lucide-react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

interface Organization {
  id: number;
  name: string;
  phone: string;
  address: string;
  description: string;
  featured_image: string;
  tokens: number;
  users: any[];
  owner: any;
}

interface OrganizationHeaderProps {
  organization: Organization;
}

export const OrganizationHeader = ({ organization }: OrganizationHeaderProps) => {
  return (
    <Card className="border-none shadow-lg bg-gradient-to-r from-primary/5 via-white to-primary/5">
      <CardHeader className="pb-4">
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-4">
            <Avatar className="h-16 w-16 border-4 border-primary/20">
              <AvatarImage src={organization.featured_image} alt={organization.name} />
              <AvatarFallback className="bg-primary text-primary-foreground text-xl font-bold">
                {organization.name.charAt(0).toUpperCase()}
              </AvatarFallback>
            </Avatar>
            <div>
              <CardTitle className="text-2xl font-bold text-gray-900 mb-1">
                {organization.name}
              </CardTitle>
              <p className="text-muted-foreground text-sm mb-2">
                {organization.description}
              </p>
              <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                <div className="flex items-center space-x-1">
                  <MapPin className="h-4 w-4" />
                  <span>{organization.address}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <Phone className="h-4 w-4" />
                  <span>{organization.phone}</span>
                </div>
              </div>
            </div>
          </div>
          <div className="text-right">
            <Badge variant="secondary" className="mb-2">
              <Users className="h-3 w-3 mr-1" />
              {organization.users.length} Users
            </Badge>
            <div className="text-sm text-muted-foreground">
              Owner: {organization.owner.name}
            </div>
          </div>
        </div>
      </CardHeader>
    </Card>
  );
};
