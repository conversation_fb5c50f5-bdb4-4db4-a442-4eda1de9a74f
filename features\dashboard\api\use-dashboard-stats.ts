import { useQuery } from "@tanstack/react-query";
import axios from "axios";
import Izi<PERSON>pi from "@/lib/endpoints";

interface DashboardStats {
  todaySales: number;
  totalSales: number;
  totalCustomers: number;
  totalProducts: number;
  totalOrders: number;
  lowStockItems: number;
  recentSales: any[];
  topProducts: any[];
  salesTrend: any[];
}

export const useDashboardStats = (organizationId: number) => {
  return useQuery({
    queryKey: ["dashboard-stats", organizationId],
    queryFn: async (): Promise<DashboardStats> => {
      const token = localStorage.getItem("izi_token");

      if (!token) {
        throw new Error("Authentication token not found");
      }

      try {
        const response = await axios.get(`${IziApi.api}/organizations/${organizationId}/dashboard/stats`, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

        if (response.status === 200) {
          return response.data.data;
        }
      } catch (error) {
        // If the API endpoint doesn't exist yet, return mock data
        console.log("Dashboard stats API not available, using mock data");
      }

      // Return mock data for development
      return {
        todaySales: 12.89,
        totalSales: 45678.90,
        totalCustomers: 156,
        totalProducts: 89,
        totalOrders: 234,
        lowStockItems: 5,
        recentSales: [],
        topProducts: [],
        salesTrend: []
      };
    },
    enabled: !!organizationId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchInterval: 30 * 1000, // Refetch every 30 seconds
  });
};
