import { useQuery } from "@tanstack/react-query";
import { Organization } from "@/features/organizations/server/Organization";

export const useCurrentOrganization = (organizationId: number) => {
  return useQuery({
    queryKey: ["current-organization", organizationId],
    queryFn: () => Organization.getById(organizationId),
    enabled: !!organizationId,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};
