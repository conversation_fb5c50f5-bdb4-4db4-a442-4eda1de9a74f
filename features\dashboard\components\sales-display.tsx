"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { TrendingUp, Calendar } from "lucide-react";
import { Badge } from "@/components/ui/badge";

interface SalesDisplayProps {
  todaySales: number;
  totalSales?: number;
  currency?: string;
  trend?: {
    value: number;
    isPositive: boolean;
  };
}

export const SalesDisplay = ({ 
  todaySales, 
  totalSales, 
  currency = "TSH", 
  trend 
}: SalesDisplayProps) => {
  const formatCurrency = (amount: number) => {
    return `${currency} ${amount.toLocaleString('en-US', { 
      minimumFractionDigits: 2, 
      maximumFractionDigits: 2 
    })}/=`;
  };

  return (
    <Card className="border-none shadow-lg bg-gradient-to-br from-green-50 to-emerald-50 hover:shadow-xl transition-all duration-300">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium text-muted-foreground">
          Today's Sales
        </CardTitle>
        <TrendingUp className="h-5 w-5 text-green-600" />
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <div>
            <div className="text-3xl font-bold text-green-700 mb-1">
              {formatCurrency(todaySales)}
            </div>
            <div className="flex items-center space-x-2">
              <Calendar className="h-3 w-3 text-muted-foreground" />
              <span className="text-xs text-muted-foreground">
                {new Date().toLocaleDateString('en-US', { 
                  weekday: 'long', 
                  year: 'numeric', 
                  month: 'long', 
                  day: 'numeric' 
                })}
              </span>
            </div>
          </div>
          
          {totalSales && (
            <div className="pt-2 border-t border-green-100">
              <div className="text-sm text-muted-foreground">Total Sales</div>
              <div className="text-lg font-semibold text-green-600">
                {formatCurrency(totalSales)}
              </div>
            </div>
          )}
          
          {trend && (
            <Badge 
              variant={trend.isPositive ? "default" : "destructive"}
              className={trend.isPositive ? "bg-green-100 text-green-800" : ""}
            >
              {trend.isPositive ? "+" : ""}{trend.value}% vs yesterday
            </Badge>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
