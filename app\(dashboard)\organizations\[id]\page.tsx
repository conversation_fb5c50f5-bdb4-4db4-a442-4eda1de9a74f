"use client";

import { use<PERSON>arams } from "next/navigation";
import { useEffect } from "react";
import {
  Package,
  Users,
  ShoppingCart,
  TrendingUp,
  AlertTriangle,
  DollarSign
} from "lucide-react";

import { useStateManager } from "@/hooks/use-context";

import { OrganizationHeader } from "@/features/dashboard/components/organization-header";
import { TokenDisplay } from "@/features/dashboard/components/token-display";
import { SalesDisplay } from "@/features/dashboard/components/sales-display";
import { StatsCard } from "@/features/dashboard/components/stats-card";
import { QuickActions } from "@/features/dashboard/components/quick-actions";

// Mock organization data
const mockOrganization = {
  id: 1,
  name: "sample",
  phone: "0626370989",
  address: "Dar es salaam",
  description: "this is sample",
  featured_image: "/storage/",
  deleted_at: null,
  created_at: "2025-07-29T20:39:46.000000Z",
  updated_at: "2025-07-29T20:39:46.000000Z",
  owner_id: 1,
  team_id: 1,
  country: null,
  region: null,
  district: null,
  ward: null,
  street: null,
  latitude: null,
  longitude: null,
  tokens: 0,
  users: [
    {
      id: 1,
      name: "<PERSON>",
      email: "<EMAIL>",
      email_verified_at: "2025-07-28T07:55:24.000000Z",
      created_at: "2025-07-28T07:55:24.000000Z",
      updated_at: "2025-07-28T07:55:24.000000Z",
      deleted_at: null,
      active_team_id: null,
      phone: "+255654303353",
      phone_verified_at: null,
      needs_password_change: true,
      apple_id: null,
      google_id: null,
      facebook_id: null,
      pivot: {
        organization_id: 1,
        user_id: 1
      }
    }
  ],
  owner: {
    id: 1,
    name: "Mark Mayalla",
    email: "<EMAIL>",
    email_verified_at: "2025-07-28T07:55:24.000000Z",
    created_at: "2025-07-28T07:55:24.000000Z",
    updated_at: "2025-07-28T07:55:24.000000Z",
    deleted_at: null,
    active_team_id: null,
    phone: "+255654303353",
    phone_verified_at: null,
    needs_password_change: true,
    apple_id: null,
    google_id: null,
    facebook_id: null
  },
  domains: []
};

// Mock dashboard stats
const mockStats = {
  todaySales: 12.89,
  totalSales: 45678.90,
  totalCustomers: 156,
  totalProducts: 89,
  totalOrders: 234,
  lowStockItems: 5,
  recentSales: [],
  topProducts: [],
  salesTrend: []
};

const OrganizationDashboard = () => {
  const params = useParams();
  const organizationId = parseInt(params.id as string);
  const { setCurrentOrganization } = useStateManager();

  useEffect(() => {
    // Set the mock organization in context
    setCurrentOrganization(mockOrganization);
  }, [setCurrentOrganization]);

  return (
    <div className="space-y-6">
      {/* Organization Header */}
      <OrganizationHeader organization={mockOrganization} />

      {/* Key Metrics Row */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Token Display */}
        <TokenDisplay
          tokens={mockOrganization.tokens}
          onAddTokens={() => {
            console.log("Add tokens clicked");
          }}
        />

        {/* Today's Sales */}
        <SalesDisplay
          todaySales={mockStats.todaySales}
          totalSales={mockStats.totalSales}
          trend={{
            value: 15.2,
            isPositive: true
          }}
        />

        {/* Total Customers */}
        <StatsCard
          title="Total Customers"
          value={mockStats.totalCustomers}
          icon={Users}
          description="Registered customers"
          trend={{
            value: 8.5,
            isPositive: true
          }}
          className="bg-gradient-to-br from-blue-50 to-cyan-50"
          valueClassName="text-blue-700"
        />

        {/* Total Products */}
        <StatsCard
          title="Products in Stock"
          value={mockStats.totalProducts}
          icon={Package}
          description="Available inventory"
          trend={{
            value: 3.2,
            isPositive: true
          }}
          className="bg-gradient-to-br from-purple-50 to-violet-50"
          valueClassName="text-purple-700"
        />
      </div>

      {/* Secondary Metrics Row */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <StatsCard
          title="Total Orders"
          value={mockStats.totalOrders}
          icon={ShoppingCart}
          description="All time orders"
          className="bg-gradient-to-br from-orange-50 to-amber-50"
          valueClassName="text-orange-700"
        />

        <StatsCard
          title="Monthly Revenue"
          value={`TSH ${mockStats.totalSales.toLocaleString()}/=`}
          icon={DollarSign}
          description="This month's earnings"
          trend={{
            value: 12.3,
            isPositive: true
          }}
          className="bg-gradient-to-br from-green-50 to-emerald-50"
          valueClassName="text-green-700"
        />

        <StatsCard
          title="Low Stock Items"
          value={mockStats.lowStockItems}
          icon={AlertTriangle}
          description="Items need restocking"
          className="bg-gradient-to-br from-red-50 to-pink-50"
          valueClassName="text-red-700"
        />
      </div>

      {/* Quick Actions */}
      <QuickActions organizationId={organizationId} />
    </div>
  );
};

export default OrganizationDashboard;
