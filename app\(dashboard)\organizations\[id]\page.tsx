"use client";

import { useParams } from "next/navigation";
import { useEffect } from "react";
import { 
  Package, 
  Users, 
  ShoppingCart, 
  TrendingUp, 
  AlertTriangle,
  DollarSign
} from "lucide-react";

import { useStateManager } from "@/hooks/use-context";
import { useCurrentOrganization } from "@/features/dashboard/api/use-current-organization";
import { useDashboardStats } from "@/features/dashboard/api/use-dashboard-stats";

import { OrganizationHeader } from "@/features/dashboard/components/organization-header";
import { TokenDisplay } from "@/features/dashboard/components/token-display";
import { SalesDisplay } from "@/features/dashboard/components/sales-display";
import { StatsCard } from "@/features/dashboard/components/stats-card";
import { QuickActions } from "@/features/dashboard/components/quick-actions";

const OrganizationDashboard = () => {
  const params = useParams();
  const organizationId = parseInt(params.id as string);
  const { setCurrentOrganization } = useStateManager();

  const { data: organization, isLoading: isLoadingOrg } = useCurrentOrganization(organizationId);
  const { data: stats, isLoading: isLoadingStats } = useDashboardStats(organizationId);

  useEffect(() => {
    if (organization) {
      setCurrentOrganization(organization);
    }
  }, [organization, setCurrentOrganization]);

  if (isLoadingOrg) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading organization...</p>
        </div>
      </div>
    );
  }

  if (!organization) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Organization Not Found</h1>
          <p className="text-muted-foreground">The organization you're looking for doesn't exist.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Organization Header */}
      <OrganizationHeader organization={organization} />

      {/* Key Metrics Row */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Token Display */}
        <TokenDisplay 
          tokens={organization.tokens} 
          onAddTokens={() => {
            // TODO: Implement add tokens functionality
            console.log("Add tokens clicked");
          }}
        />

        {/* Today's Sales */}
        <SalesDisplay 
          todaySales={stats?.todaySales || 12.89}
          totalSales={stats?.totalSales}
          trend={stats ? {
            value: 15.2,
            isPositive: true
          } : undefined}
        />

        {/* Total Customers */}
        <StatsCard
          title="Total Customers"
          value={stats?.totalCustomers || 0}
          icon={Users}
          description="Registered customers"
          trend={{
            value: 8.5,
            isPositive: true
          }}
          className="bg-gradient-to-br from-blue-50 to-cyan-50"
          valueClassName="text-blue-700"
        />

        {/* Total Products */}
        <StatsCard
          title="Products in Stock"
          value={stats?.totalProducts || 0}
          icon={Package}
          description="Available inventory"
          trend={{
            value: 3.2,
            isPositive: true
          }}
          className="bg-gradient-to-br from-purple-50 to-violet-50"
          valueClassName="text-purple-700"
        />
      </div>

      {/* Secondary Metrics Row */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <StatsCard
          title="Total Orders"
          value={stats?.totalOrders || 0}
          icon={ShoppingCart}
          description="All time orders"
          className="bg-gradient-to-br from-orange-50 to-amber-50"
          valueClassName="text-orange-700"
        />

        <StatsCard
          title="Monthly Revenue"
          value={`TSH ${(stats?.totalSales || 0).toLocaleString()}/=`}
          icon={DollarSign}
          description="This month's earnings"
          trend={{
            value: 12.3,
            isPositive: true
          }}
          className="bg-gradient-to-br from-green-50 to-emerald-50"
          valueClassName="text-green-700"
        />

        <StatsCard
          title="Low Stock Items"
          value={stats?.lowStockItems || 0}
          icon={AlertTriangle}
          description="Items need restocking"
          className="bg-gradient-to-br from-red-50 to-pink-50"
          valueClassName="text-red-700"
        />
      </div>

      {/* Quick Actions */}
      <QuickActions organizationId={organizationId} />

      {/* Loading overlay for stats */}
      {isLoadingStats && (
        <div className="fixed inset-0 bg-black/20 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg shadow-lg">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary mx-auto mb-2"></div>
            <p className="text-sm text-muted-foreground">Loading dashboard data...</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default OrganizationDashboard;
